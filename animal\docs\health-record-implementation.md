# Health Record System - Technical Implementation Guide

## Architecture Overview

The health record system is implemented across three main components:

1. **Chat Interface** (`animal/app/chat.tsx`) - Natural language input processing
2. **MCP Server** (`mcp-server/index.js`) - AI analysis and business logic  
3. **Records Screen** (`animal/app/records/add.tsx`) - Traditional form interface

## Data Flow

```
User Input → Chat Interface → MCP Server → AI Analysis → Database → Response
```

### 1. User Input Processing
- User types natural language in chat
- Chat interface sends to MCP server with context
- Server analyzes request type using pattern matching

### 2. AI Analysis
- OpenAI GPT-4 extracts structured data from text
- Validates required fields
- Generates missing information

### 3. Database Operations
- Saves to Firestore under farm/animal/records collection
- Updates animal health status
- Maintains audit trail

## Key Components

### Request Type Detection
```javascript
// In mcp-server/index.js
const analyzeRequestType = (prompt, hasImage) => {
  const lowerPrompt = (prompt || '').toLowerCase();
  
  // Health record patterns (VERY SPECIFIC)
  if (lowerPrompt.includes('record vaccination') ||
      lowerPrompt.includes('log vaccination') ||
      lowerPrompt.includes('add vaccination') ||
      // ... more patterns
      ) {
    return 'add_health_record';
  }
  // ... other patterns
};
```

### AI Prompt Engineering
```javascript
// In mcp-server/services/ai-service.js
if (context === 'add_health_record') {
  systemMessage = `You are an expert at extracting health record information...
  
  Return ONLY a JSON object with this structure:
  {
    "animalName": "exact animal name as written by user or null",
    "recordType": "vaccination|treatment|deworming|checkup|other",
    "recordOption": "specific vaccine/medicine/treatment name or null",
    "practitioner": "exact practitioner name or 'self'",
    "date": "exact date as written or null",
    "notes": "any additional notes or auto-generated",
    "action": "add|record|log|save|other",
    "confidence": "confidence level 1-100"
  }`;
}
```

### Data Structure
```typescript
interface HealthRecord {
  id: string;
  farmId: string;
  farmName: string;
  animalId: string;
  animalName: string;
  recordType: 'vaccination' | 'treatment' | 'deworming' | 'checkup' | 'other';
  recordOption: string;
  practitioner: string;
  notes: string;
  date: string; // ISO 8601
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}
```

## Implementation Details

### 1. Pattern Matching
The system uses specific patterns to detect health record requests:

```javascript
// High priority patterns for health records
if (lowerPrompt.includes('record vaccination') ||
    lowerPrompt.includes('log vaccination') ||
    lowerPrompt.includes('add vaccination') ||
    lowerPrompt.includes('vaccinated') ||
    lowerPrompt.includes('gave medicine') ||
    lowerPrompt.includes('administered') ||
    lowerPrompt.includes('treated') ||
    lowerPrompt.includes('dewormed')) {
  return 'add_health_record';
}
```

### 2. AI Analysis Function
```javascript
const healthRecordInfo = await analyzePromptWithAI(prompt, 'add_health_record');
```

The AI extracts:
- Animal name/identifier
- Record type (vaccination, treatment, etc.)
- Specific treatment details
- Practitioner information
- Date/time information
- Additional notes

### 3. Animal Selection Logic
```javascript
// Check if animal was mentioned in prompt
let selectedAnimal = null;
if (healthRecordInfo && healthRecordInfo.animalName) {
  selectedAnimal = allAnimals.find(animal =>
    animal.name?.toLowerCase().includes(healthRecordInfo.animalName.toLowerCase())
  );
}

// If no animal found, show selection interface
if (!selectedAnimal && allAnimals.length > 0) {
  // Return animal selection UI with images
  return res.json({
    message: selectionMessage,
    animalImages: animalImages,
    context: {
      healthRecordData: healthRecordInfo,
      selectedFarm,
      availableAnimals: allAnimals,
      needsAnimalSelection: true
    }
  });
}
```

### 4. Record Creation
```javascript
const recordData = {
  animalId: selectedAnimal.id,
  date: healthRecordData.date ? new Date(healthRecordData.date).getTime() : currentDate.getTime(),
  type: healthRecordData.recordType,
  title: `${healthRecordData.recordType}: ${healthRecordData.recordOption || 'General treatment'}`,
  description: healthRecordData.notes || `${healthRecordData.recordType} administered to ${selectedAnimal.name}`,
  symptoms: [],
  practitioner: healthRecordData.practitioner || 'self',
  recordOption: healthRecordData.recordOption || 'General treatment',
  createdBy: userId,
  createdAt: currentDate.getTime(),
  updatedAt: currentDate.getTime()
};
```

### 5. Database Storage
```javascript
// Save to animal's records subcollection
const farmRef = firestore.collection('farms').doc(selectedFarm.id);
const animalRef = farmRef.collection('animals').doc(selectedAnimal.id);
const recordRef = await animalRef.collection('records').add(recordData);
```

## Context Management

### Chat Context Structure
```javascript
context: {
  healthRecordData: extractedInfo,
  selectedFarm: farmObject,
  selectedAnimal: animalObject,
  availableAnimals: animalArray,
  needsAnimalSelection: boolean,
  readyToSave: boolean
}
```

### State Transitions
1. **Initial Request** → Extract basic info, show animal selection if needed
2. **Animal Selected** → Complete record data, show confirmation
3. **Confirmation** → Save to database, show success message

## Error Handling

### Missing Information
```javascript
// Validate required fields
if (!healthRecordInfo?.recordType) {
  return res.json({
    message: 'Health record type is required (e.g., vaccination, treatment, deworming, checkup).',
    error: true
  });
}
```

### Animal Not Found
```javascript
if (allAnimals.length === 0) {
  return res.json({
    message: 'No animals found in this farm. Please add animals first.',
    error: true
  });
}
```

### Save Failures
```javascript
try {
  const recordRef = await animalRef.collection('records').add(recordData);
  // Success handling
} catch (error) {
  console.error('Error saving health record:', error);
  return res.json({ 
    message: 'Failed to save health record. Please try again.',
    error: true 
  });
}
```

## Integration Points

### Chat Interface Integration
```typescript
// In chat.tsx - Handle health record responses
if (data.healthRecordData) {
  console.log('🏥 EXTRACTED HEALTH RECORD DATA:');
  console.log(JSON.stringify(data.healthRecordData, null, 2));
}

// Handle animal selection for health records
const handleAnimalSelection = async (animalId: string, animalName: string) => {
  // Send animal selection back to server
};
```

### Records Screen Integration
The traditional records screen (`records/add.tsx`) provides:
- Dropdown selection for record types
- Species-specific record options
- Form validation
- Practitioner selection with voice input

### Database Schema
```
farms/{farmId}/animals/{animalId}/records/{recordId}
{
  animalId: string,
  date: number, // timestamp
  type: RecordType,
  title: string,
  description: string,
  symptoms: string[],
  practitioner: string,
  recordOption: string,
  createdBy: string,
  createdAt: number,
  updatedAt: number
}
```

## Testing Scenarios

### 1. Complete Information
Input: "Record FMD vaccination for Daisy by Dr. Ali today"
Expected: Direct confirmation with all fields populated

### 2. Missing Animal
Input: "Add vaccination record"
Expected: Animal selection interface

### 3. Missing Details
Input: "Vaccinated Lily"
Expected: Request for vaccine type or show options

### 4. Multiple Animals
Input: "Dewormed all cows"
Expected: Individual record creation for each cow

## Performance Considerations

### AI Response Time
- Average response time: 2-3 seconds
- Timeout handling: 30 seconds
- Fallback to form interface if AI fails

### Database Operations
- Batch operations for multiple animals
- Optimistic updates in UI
- Error recovery mechanisms

### Caching
- Cache animal lists per farm
- Cache record options by species
- Cache user preferences

## Security Considerations

### Input Validation
- Sanitize all user inputs
- Validate record types against enum
- Check user permissions for farm access

### Data Privacy
- Encrypt sensitive health information
- Audit trail for all record changes
- User-based access controls

### API Security
- Rate limiting on AI requests
- Authentication for all endpoints
- Input size limits

This implementation provides a robust, user-friendly system for health record management while maintaining data integrity and system performance.
