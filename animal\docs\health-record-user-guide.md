# Health Record Management - User Guide

## Overview
The health record system allows you to quickly add animal health records through natural conversation in the chat interface. Simply describe what health activity you performed, and the AI will extract the details and guide you through the process.

## How to Add Health Records

### Method 1: Complete Sentence
Provide all details in one message:

**Examples:**
- "Record a vaccination for <PERSON> with the FMD vaccine. Dr<PERSON> administered it today."
- "I gave deworming medicine to cow 123 yesterday."
- "<PERSON> was treated for fever with antibiotics by Dr. <PERSON>."
- "Checkup done for <PERSON><PERSON>, everything looks normal."

### Method 2: Step-by-Step
Start with basic information and let the AI guide you:

**Examples:**
- "Add vaccination record"
- "Log treatment for today"
- "Record deworming"
- "Add health checkup"

## Required Information

### Mandatory Fields
1. **Farm Name** - Automatically uses the farm shown in chat header
2. **Record Type** - vaccination, treatment, deworming, checkup, or other
3. **Record Option** - Specific vaccine/medicine/treatment details
4. **Animal** - Which animal received the treatment
5. **Practitioner** - Who performed the treatment (defaults to "self")
6. **Date** - When it was done (defaults to current date)
7. **Notes** - Additional details (auto-generated if not provided)

### Optional Fields
- Specific practitioner name
- Custom notes and observations
- Specific date/time

## Supported Record Types

### 1. Vaccination
Record immunizations and vaccines:
- **Common Options**: FMD Vaccine, HS Vaccine, BQ Vaccine, PPR Vaccine
- **Example**: "Vaccinated Lily with FMD vaccine"

### 2. Treatment  
Record medical treatments:
- **Common Options**: Antibiotics, Pain Relievers, Anti-inflammatories
- **Example**: "Treated Bella for fever with antibiotics"

### 3. Deworming
Record parasite control:
- **Common Options**: Anthelmintics, Dewormers, specific brands
- **Example**: "Dewormed all goats with broad spectrum medicine"

### 4. Checkup
Record health examinations:
- **Common Options**: General Health Check, Pre-breeding Check
- **Example**: "Health checkup for Moti, all normal"

### 5. Other
Any other health-related activity:
- **Example**: "Applied wound care to injured leg"

## Language Support

### English Examples
- "Record vaccination for Daisy"
- "I gave medicine to cow 123"
- "Dr. Ali treated the goat"
- "Add health record for today"

### Roman Urdu Examples  
- "Daisy ko vaccination diya"
- "Gaye ko dawai di"
- "Doctor ne bakre ka ilaj kiya"
- "Aaj health record add karo"

## Animal Selection

### If Animal is Mentioned
The system will automatically find the animal:
- "Vaccinated Daisy" → Finds animal named Daisy
- "Treated cow 123" → Finds animal with tag/name "cow 123"

### If Animal is Not Mentioned
The system will show all animals with pictures:
- Click on animal image to select
- Or type the number shown next to animal name
- Or type the animal's name

## Practitioner Options

### Self Treatment
If you performed the treatment yourself:
- "I gave medicine to Bella"
- "Vaccinated the cow myself"
- System automatically sets practitioner to "self"

### Veterinarian Treatment
If a vet performed the treatment:
- "Dr. Ali vaccinated Daisy"
- "Vet treated the goat for fever"
- System extracts the doctor's name

### Default Behavior
If no practitioner is mentioned, system assumes "self"

## Date Handling

### Natural Language Dates
- "today" → Current date
- "yesterday" → Previous day  
- "last week" → 7 days ago
- "this morning" → Today with morning time

### Specific Dates
- "January 15" → Specific date
- "2024-01-15" → ISO format date
- "15/01/2024" → DD/MM/YYYY format

### Default Behavior
If no date mentioned, uses current date and time

## Confirmation Process

### Review Information
Before saving, the system shows:
- Selected farm and animal
- Record type and details
- Practitioner and date
- Generated notes

### Save the Record
Confirm by typing any of these:
- **English**: "save", "yes", "ok", "confirm", "add it"
- **Urdu**: "محفوظ کرو", "ہاں", "ٹھیک ہے", "شامل کرو"

## Example Conversations

### Complete Vaccination Record
```
User: "Record FMD vaccination for Daisy by Dr. Ali today"

AI: "✅ Health Record Ready!
🏡 Farm: Green Valley Farm  
🐄 Animal: Daisy (Cow)
📋 Type: vaccination
💊 Details: FMD vaccination
👨‍⚕️ Practitioner: Dr. Ali
📅 Date: January 15, 2024
📝 Notes: FMD vaccination administered to Daisy by Dr. Ali

💾 Type 'Yes' to save this record."

User: "yes"

AI: "✅ Health Record Saved Successfully!"
```

### Step-by-Step Process
```
User: "Add vaccination record"

AI: "🏥 Add Health Record
📋 Type: vaccination
🐄 Select Animal:" [Shows animal images]

User: [Clicks on Daisy's image]

AI: "✅ Animal Selected: Daisy
Please specify which vaccine was used."

User: "FMD vaccine by Dr. Ali"

AI: "✅ Health Record Ready!
[Shows complete details]
💾 Type 'Yes' to save."

User: "save"

AI: "✅ Health Record Saved Successfully!"
```

## Tips for Best Results

### Be Specific
- Include animal name when possible
- Mention specific vaccines/medicines
- Include practitioner if not yourself

### Use Natural Language
- "I vaccinated Daisy today" ✅
- "vaccination Daisy today" ❌

### Check Information
- Review all details before confirming
- Correct any mistakes before saving

### Multiple Records
- Add one record at a time for accuracy
- Use clear, separate messages for each animal

## Troubleshooting

### Animal Not Found
If system can't find the mentioned animal:
- Check spelling of animal name
- Use the animal selection interface
- Verify animal exists in current farm

### Missing Information
If system asks for more details:
- Provide the requested information
- Use the guided interface
- Be more specific in your description

### Record Not Saving
If save fails:
- Check internet connection
- Try again with "yes" or "save"
- Contact support if issue persists

## Integration with Records Screen

### View Saved Records
- Go to Animals → [Animal Name] → Records
- See complete history of all health records
- Filter by record type or date

### Edit Records
- Use the Records → Add screen for detailed entry
- Edit existing records through animal details
- Export records for veterinary visits

This system makes health record keeping fast and accurate while maintaining detailed documentation for your farm management needs.
